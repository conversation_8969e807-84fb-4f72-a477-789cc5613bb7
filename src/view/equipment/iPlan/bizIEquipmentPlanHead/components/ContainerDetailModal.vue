<template>
  <section>
    <!-- 操作按钮区域 -->
    <div class="cs-action-btn">
      <div class="cs-action-btn-item">
        <a-button size="small" @click="handleAdd">
          <template #icon>
            <GlobalIcon type="plus" style="color:green"/>
          </template>
          新增
        </a-button>
      </div>
      <div class="cs-action-btn-item">
        <a-button size="small" @click="handleDelete" :disabled="selectedRowKeys.length === 0">
          <template #icon>
            <GlobalIcon type="delete" style="color:red"/>
          </template>
          删除
        </a-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <a-table
      ref="tableRef"
      class="cs-action-item"
      size="small"
      :scroll="{ y: 400, x: 800 }"
      bordered
      :pagination="false"
      :columns="tableColumns"
      :data-source="tableData"
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      row-key="id"
      :loading="tableLoading"
    >
      <!-- 行内编辑模板 -->
      <template #bodyCell="{ text, record, index, column }">
        <template v-if="column.dataIndex === 'containerType'">
          <div>
            <!-- 调试信息 -->
            <div v-if="record.id.includes('_add')" style="font-size: 10px; color: red;">
              调试: ID={{ record.id }}, 编辑状态={{ !!editableData[record.id] }}
            </div>
            <a-select
              v-if="editableData[record.id]"
              v-model:value="editableData[record.id][column.dataIndex]"
              size="small"
              style="width: 100%"
              placeholder="请选择箱型"
              show-search
              :filter-option="filterOption"
              @change="handleContainerTypeChange(record.id, $event)"
            >
              <a-select-option v-for="item in containerOptions" :key="item.value || item.code" :value="item.value || item.code">
                {{ item.label || item.name }}
              </a-select-option>
            </a-select>
            <template v-else>
              {{ getContainerTypeDisplay(text) }}
            </template>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'unitPriceTotal'">
          <div>
            <a-input-number
              v-if="editableData[record.id]"
              v-model:value="editableData[record.id][column.dataIndex]"
              size="small"
              style="width: 100%"
              :precision="6"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              :disabled="true"
              placeholder="选择箱型后自动填充"
            />
            <template v-else>
              {{ formatNumber(text, 6) }}
            </template>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'containerCount'">
          <div>
            <a-input-number
              v-if="editableData[record.id]"
              v-model:value="editableData[record.id][column.dataIndex]"
              size="small"
              style="width: 100%"
              :precision="6"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              @blur="handleAmountCalculation(record.id)"
              placeholder="请输入箱数"
            />
            <template v-else>
              {{ formatNumber(text, 6) }}
            </template>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'amount'">
          <div>
            <a-input-number
              v-if="editableData[record.id]"
              v-model:value="editableData[record.id][column.dataIndex]"
              size="small"
              style="width: 100%"
              :precision="2"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              :disabled="true"
              placeholder="自动计算"
            />
            <template v-else>
              {{ formatNumber(text, 2) }}
            </template>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'operation'">
          <div>
            <!-- 调试信息 -->
            <div v-if="record.id.includes('_add')" style="font-size: 10px; color: blue;">
              操作调试: {{ record.id }}, 编辑={{ !!editableData[record.id] }}
            </div>
            <a-button
              v-if="editableData[record.id]"
              type="link"
              size="small"
              @click="save(record.id)"
            >
              保存
            </a-button>
            <a-button
              v-if="editableData[record.id]"
              type="link"
              size="small"
              @click="cancel(record.id)"
            >
              取消
            </a-button>
            <a-button
              v-else
              type="link"
              size="small"
              @click="edit(record.id)"
            >
              编辑
            </a-button>
          </div>
        </template>
      </template>
    </a-table>

    <!-- 调试信息 -->
    <div style="margin: 10px 0; padding: 10px; background: #f0f0f0; font-size: 12px;">
      <div>表格数据数量: {{ tableData.length }}</div>
      <div>编辑数据: {{ editableData ? Object.keys(editableData).join(', ') : '无' }}</div>
      <div v-for="item in tableData" :key="item.id" style="margin: 2px 0;">
        ID: {{ item.id }}, 箱型: {{ item.containerType || '空' }}, 编辑状态: {{ !!editableData[item.id] }}
      </div>
    </div>

    <!-- 合计行 -->
    <div class="total-row">
      <span>合计：{{ formatNumber(totalAmount, 2) }}</span>
    </div>

    <!-- 底部按钮 -->
    <div class="cs-form-action">
      <a-button type="primary" @click="handleSave" :loading="saveLoading">保存</a-button>
      <a-button @click="handleReturn">返回</a-button>
    </div>
  </section>
</template>

<script setup>
import { computed, onMounted, reactive, ref, nextTick } from "vue";
import { message } from "ant-design-vue";
import {
  getEquipmentContainerInfoList,
  insertEquipmentContainerInfo,
  updateEquipmentContainerInfo,
  deleteEquipmentContainerInfo,
  getContainerList,
  getContainerMes
} from "@/api/equipment/equipmentPlanApi";
import { useColumnsRender } from "@/view/common/useColumnsRender";
import { deepClone } from "@/view/utils/common";

defineOptions({
  name: 'ContainerDetailModal'
});

const props = defineProps({
  headId: {
    type: String,
    required: true
  },
  businessType: {
    type: String,
    default: '3' // 默认为3-国营贸易进口烟机设备
  }
});

const emit = defineEmits(['cancel', 'save-success']);

const { formatNumber } = useColumnsRender();

// 表格相关状态
const tableRef = ref();
const tableData = ref([]);
const selectedRowKeys = ref([]);
const editableData = ref({});
const tableLoading = ref(false);
const saveLoading = ref(false);

// 箱型选项
const containerOptions = ref([]);

// 表格列定义
const tableColumns = [
  {
    title: '操作',
    dataIndex: 'operation',
    width: 120,
    align: 'center',
    fixed: 'left'
  },
  {
    title: '箱型',
    dataIndex: 'containerType',
    width: 200,
    align: 'center'
  },
  {
    title: '单价合计',
    dataIndex: 'unitPriceTotal',
    width: 150,
    align: 'center'
  },
  {
    title: '箱数',
    dataIndex: 'containerCount',
    width: 120,
    align: 'center'
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: 150,
    align: 'center'
  }
];

// 计算合计金额
const totalAmount = computed(() => {
  return tableData.value.reduce((sum, item) => {
    return sum + (parseFloat(item.amount) || 0);
  }, 0);
});

// 获取箱型显示文本
const getContainerTypeDisplay = (code) => {
  const container = containerOptions.value.find(item => (item.value || item.code) === code);
  return container ? (container.label || container.name) : code;
};

// 过滤选项
const filterOption = (input, option) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 箱型变更处理
const handleContainerTypeChange = async (id, containerType) => {
  try {
    if (!containerType) return;

    // 调用获取单价合计信息接口
    const result = await getContainerMes(props.businessType, containerType);
    if (result.code === 200 && result.data) {
      // 更新单价合计
      editableData.value[id].unitPriceTotal = result.data.unitPriceTotal || 0;
      // 重新计算金额
      handleAmountCalculation(id);
    } else {
      message.error(result.message || '获取单价合计信息失败');
    }
  } catch (error) {
    console.error('获取单价合计信息失败:', error);
    message.error('获取单价合计信息失败');
  }
};

// 金额计算
const handleAmountCalculation = (id) => {
  const editData = editableData.value[id];
  if (editData && editData.unitPriceTotal && editData.containerCount) {
    editData.amount = parseFloat((editData.unitPriceTotal * editData.containerCount).toFixed(2));
  }
};

// 表格行选择
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 编辑行
const edit = (id) => {
  const target = tableData.value.find(item => item.id === id);
  editableData.value[id] = { ...target };
};

// 保存行
const save = async (id) => {
  try {
    const editData = editableData.value[id];

    // 验证必填字段
    if (!editData.containerType) {
      message.error('箱型不能为空');
      return;
    }
    if (!editData.unitPriceTotal) {
      message.error('单价合计不能为空，请先选择箱型');
      return;
    }
    if (!editData.containerCount) {
      message.error('箱数不能为空');
      return;
    }

    // 确保进行金额计算
    handleAmountCalculation(id);

    // 更新表格数据
    const target = tableData.value.find(item => item.id === id);
    Object.assign(target, editData);

    // 清除编辑状态
    delete editableData.value[id];

    message.success('行保存成功');
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败');
  }
};

// 取消编辑
const cancel = (id) => {
  delete editableData.value[id];
};

// 新增行
const handleAdd = () => {
  const newId = Date.now().toString() + '_add';
  const newRecord = {
    id: newId,
    headId: props.headId,
    containerType: '测试箱型',
    unitPriceTotal: 100,
    containerCount: 1,
    amount: 100
  };

  console.log('准备新增行:', newRecord);
  console.log('新增前表格数据:', JSON.stringify(tableData.value));
  console.log('新增前编辑数据:', JSON.stringify(editableData.value));

  // 先设置编辑状态
  editableData.value = {
    ...editableData.value,
    [newId]: { ...newRecord }
  };

  // 再添加到表格数据
  tableData.value = [
    ...tableData.value,
    newRecord
  ];

  console.log('新增后表格数据:', JSON.stringify(tableData.value));
  console.log('新增后编辑数据:', JSON.stringify(editableData.value));
  console.log('新行是否在编辑状态:', !!editableData.value[newId]);

  message.success(`新增行成功，ID: ${newId}`);
};

// 删除行
const handleDelete = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的数据');
    return;
  }

  try {
    // 分离需要调用API删除的数据和本地删除的数据
    const toDeleteFromApi = [];
    const toDeleteLocally = [];

    selectedRowKeys.value.forEach(id => {
      if (id.toString().includes('add')) {
        // 新增的数据，只需本地删除
        toDeleteLocally.push(id);
      } else {
        // 已存在的数据，需要调用API删除
        toDeleteFromApi.push(id);
      }
    });

    // 调用API删除已存在的数据
    if (toDeleteFromApi.length > 0) {
      await deleteEquipmentContainerInfo(toDeleteFromApi.join(','));
    }

    // 从表格中移除所有选中的数据
    tableData.value = tableData.value.filter(item => !selectedRowKeys.value.includes(item.id));
    selectedRowKeys.value = [];
    message.success('删除成功');
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  }
};

// 保存所有数据
const handleSave = async () => {
  try {
    saveLoading.value = true;

    // 验证所有行数据
    for (const item of tableData.value) {
      if (!item.containerType) {
        message.error('箱型不能为空');
        return;
      }
      if (!item.unitPriceTotal) {
        message.error('单价合计不能为空');
        return;
      }
      if (!item.containerCount) {
        message.error('箱数不能为空');
        return;
      }
    }

    // 批量保存数据
    const savePromises = tableData.value.map(item => {
      const saveData = {
        ...item,
        headId: props.headId
      };

      if (item.id && !item.id.toString().includes('add')) {
        // 更新现有数据
        return updateEquipmentContainerInfo(item.id, saveData);
      } else {
        // 新增数据
        delete saveData.id; // 移除临时ID
        return insertEquipmentContainerInfo(saveData);
      }
    });

    await Promise.all(savePromises);
    message.success('保存成功');
    emit('save-success');
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败');
  } finally {
    saveLoading.value = false;
  }
};

// 返回
const handleReturn = () => {
  emit('cancel');
};

// 获取箱型选项
const loadContainerOptions = async () => {
  try {
    const result = await getContainerList(props.businessType);
    if (result.code === 200) {
      // 确保数据格式正确
      containerOptions.value = result.data || [];
    } else {
      console.warn('获取箱单列表失败:', result.message);
      containerOptions.value = [];
    }
  } catch (error) {
    console.error('获取箱单列表失败:', error);
    containerOptions.value = [];
  }
};

// 加载表格数据
const loadTableData = async () => {
  try {
    tableLoading.value = true;
    const params = {
      headId: props.headId,
      page: 1,
      limit: 1000 // 获取所有数据
    };

    const result = await getEquipmentContainerInfoList(params);
    if (result.code === 200) {
      // 确保每行数据都有唯一的ID
      tableData.value = (result.data || []).map(item => ({
        ...item,
        id: item.id || item.sid || Date.now() + Math.random()
      }));
    } else {
      message.error(result.message || '获取数据失败');
      tableData.value = [];
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败');
    tableData.value = [];
  } finally {
    tableLoading.value = false;
  }
};

// 组件挂载时初始化
onMounted(async () => {
  await loadContainerOptions();
  await loadTableData();
});
</script>

<style lang="less" scoped>
.cs-action-btn {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.total-row {
  margin: 16px 0;
  text-align: right;
  font-weight: bold;
  font-size: 14px;
}

.cs-form-action {
  margin-top: 16px;
  text-align: center;
}

.cs-form-action .ant-btn {
  margin: 0 8px;
}
</style>
